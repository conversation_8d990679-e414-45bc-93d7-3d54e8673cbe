import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { OutgoingMessageEntity } from '@message-hub/domain/entities/outgoing-message.entity';
import { OutgoingMessagePort } from '@message-hub/infrastructure/ports/db/outgoing-message.port';
import { Prisma } from '@prisma/client';
import { randomUUID } from 'crypto';
import { CommunicationChannel } from '@common/enums';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { logger } from '@edutalent/commons-sdk';

@Injectable()
export class OutgoingMessageAdapter
  extends PrismaCommonAdapter<OutgoingMessageEntity>
  implements OutgoingMessagePort {
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'outgoingMessage');
  }

  async insertOutgoingMessage(
    {
      customerId,
      from,
      to,
      messageType,
      message,
      channel,
      status,
      isFirstMessage,
      apiUrl,
      fileUrl,
    }: {
      customerId: string;
      from: string;
      to: string;
      messageType: string;
      message: string;
      channel: string;
      status: string;
      isFirstMessage: boolean;
      apiUrl: string;
      fileUrl: string;
    },
    randomDelay: number,
  ): Promise<void> {
    try {
      return this.prisma.client.$transaction(async tx => {
        // Get customer phone to check daily limit
        const customerPhone = await tx.customerPhone.findFirst({
          where: { phoneNumber: from, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(`Customer phone not found for number: ${from} and channel: ${channel}`);
        }

        // Get the latest scheduled message time
        const result = (await tx.$queryRaw(Prisma.sql`
          SELECT "time_to_go" as "maxTimeToGo"
          FROM "message_hub"."outgoing_message"
          WHERE "sent" = false
            AND communication_channel = ${channel}
            AND "from" = ${from}
          ORDER BY "time_to_go" DESC LIMIT 1
          FOR UPDATE
        `)) as { maxTimeToGo: Date }[];

        let newTimeToGo: Date;
        const lastTimeToGo = result[0]?.maxTimeToGo || new Date();

        // If it's not a first message, send it immediately
        if (!isFirstMessage || process.env.NODE_ENV === 'development') {
          newTimeToGo = new Date(new Date().getTime() + Number(randomDelay) * 1000);
        } else {
          // Set the time to go to the next business day at 8 AM Brazil time
          newTimeToGo = new Date(lastTimeToGo.getTime() + Number(randomDelay) * 1000);
          newTimeToGo.setHours(8, 0, 0, 0);

          // Function to check if a date is weekend
          const isWeekend = (date: Date): boolean => {
            const day = date.getDay();
            const hours = date.getHours();

            // Saturday (6) is weekend only after 14:00 (2 PM) Brazil time
            if (day === 6 && hours >= 14) {
              return true;
            }

            // Sunday (0) is always weekend
            if (day === 0) {
              return true;
            }

            return false;
          };

          // Function to get next business day
          const getNextBusinessDay = (date: Date): Date => {
            const nextDay = new Date(date);
            nextDay.setDate(nextDay.getDate() + 1);
            if (isWeekend(nextDay)) {
              return getNextBusinessDay(nextDay);
            }
            return nextDay;
          };

          // Function to count scheduled messages for a specific day
          const countScheduledMessagesForDay = async (date: Date): Promise<number> => {
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            const [count] = (await tx.$queryRaw(Prisma.sql`
              SELECT COUNT(*) as count
              FROM "message_hub"."outgoing_message"
              WHERE "from" = ${from}
                AND "communication_channel" = ${channel}
                AND "is_first_message" = true
                AND "time_to_go" >= ${startOfDay.toISOString()}::timestamp
                AND "time_to_go" <= ${endOfDay.toISOString()}::timestamp
            `)) as [{ count: number }];

            return count.count;
          };

          // Skip weekends
          if (isWeekend(newTimeToGo)) {
            newTimeToGo = getNextBusinessDay(newTimeToGo);
            newTimeToGo.setHours(11, 0, 0, 0);
          }

          // Check if we've reached the daily limit for the current day
          let currentDayCount = await countScheduledMessagesForDay(newTimeToGo);

          // If current day is full, find the next available day
          while (currentDayCount >= customerPhone.dailyLimit) {
            newTimeToGo = getNextBusinessDay(newTimeToGo);
            newTimeToGo.setHours(11, 0, 0, 0);
            currentDayCount = await countScheduledMessagesForDay(newTimeToGo);
          }

          // Now that we have a day with available spots, get the latest timeToGo for that day
          const latestMessage = (await tx.$queryRaw(Prisma.sql`
            SELECT "time_to_go" as "maxTimeToGo"
            FROM "message_hub"."outgoing_message"
            WHERE "sent" = false
              AND communication_channel = ${channel}
              AND "from" = ${from}
              AND "time_to_go" >= ${newTimeToGo.toISOString()}::timestamp
              AND "time_to_go" < ${new Date(
            newTimeToGo.getTime() + 24 * 60 * 60 * 1000,
          ).toISOString()}::timestamp
            ORDER BY "time_to_go" DESC LIMIT 1
            FOR UPDATE
          `)) as { maxTimeToGo: Date }[];

          // If there are no messages for this day yet, start from the base time
          if (!latestMessage || latestMessage.length === 0) {
            newTimeToGo = new Date(newTimeToGo.getTime() + Number(randomDelay) * 1000);
          } else {
            // If there are messages, add random delay to the latest one
            newTimeToGo = new Date(
              latestMessage[0].maxTimeToGo.getTime() + Number(randomDelay) * 1000,
            );
          }

          // If it's after 23:00, schedule for next business day
          if (newTimeToGo.getHours() >= 23) {
            newTimeToGo = getNextBusinessDay(newTimeToGo);
            newTimeToGo.setHours(11, 0, 0, 0);
          }
        }

        await tx.$executeRaw(
          Prisma.sql`
            INSERT INTO "message_hub"."outgoing_message" ("id", "customer_id", "to", "from", "message_type",
                                                          "message",
                                                          "communication_channel", "time_to_go", "sent",
                                                          "status", "created_at", "updated_at",
                                                          "api_url", "file_url", "is_first_message")
            VALUES (${randomUUID()}::uuid, ${customerId}::uuid, ${to}, ${from}, ${messageType}, ${message},
                    ${channel}, ${newTimeToGo.toISOString()}::timestamp, false,
                    ${status}, NOW(), NOW(), ${apiUrl}, ${fileUrl}, ${isFirstMessage})
          `,
        );
      });
    } catch (error) {
      throw new Error(`Error inserting outgoing message: ${error}`);
    }
  }

  async processOutgoingMessage(
    fromNumber: string,
    channel: CommunicationChannel,
    sendMessage: (to: string, message: string, apiUrl: string) => Promise<void>,
    sendMessageWithFile: (
      to: string,
      message: string,
      apiUrl: string,
      fileUrl: string,
      fileType: string,
    ) => Promise<void>,
    sendMessageSMS: (from: string, to: string, text: string, apiUrl: string) => Promise<void>,
  ): Promise<void> {
    return this.prisma.client.$transaction(
      async tx => {
        const messages = (await tx.$queryRaw(Prisma.sql`
          SELECT "id",
                 "from",
                 "to",
                 "message",
                 "time_to_go"            AS "timeToGo",
                 "communication_channel" AS "channel",
                 "api_url"               AS "apiUrl",
                 "message_type"          AS "messageType",
                 "file_url"              AS "fileUrl"
          FROM "message_hub"."outgoing_message"
          WHERE "sent" = false
            AND "time_to_go" <= NOW()
            AND "from" = ${fromNumber}
            AND "communication_channel" = ${channel}
          ORDER BY "time_to_go" ASC LIMIT 1
              FOR
          UPDATE SKIP LOCKED
        `)) as {
          id: string;
          from: string;
          to: string;
          message: string;
          timeToGo: Date;
          channel: string;
          apiUrl: string;
          messageType: string;
          fileUrl?: string;
        }[];

        if (!messages || messages.length === 0) {
          return;
        }

        const customerPhone = await tx.customerPhone.findFirst({
          where: { phoneNumber: fromNumber, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(
            `Customer phone not found for number: ${fromNumber} and channel: ${channel}`,
          );
        }

        const maxOutgoingDelay = customerPhone.outgoingMaxDelay;

        for (const message of messages) {
          const startSendingDate = new Date();
          try {
            if (message.channel === CommunicationChannel.WHATSAPPSELFHOSTED) {
              if (message.fileUrl) {
                await sendMessageWithFile(
                  message.to,
                  message.message,
                  message.apiUrl,
                  message.fileUrl,
                  message.messageType,
                );
              } else {
                await sendMessage(message.to, message.message, message.apiUrl);
              }
            } else if (message.channel === CommunicationChannel.SMS_VONAGE) {
              await sendMessageSMS(message.from, message.to, message.message, message.apiUrl);
            } else {
              throw new BusinessException(
                'Outgoing-Message-Adapter',
                `Channel ${message.channel} not supported for number: ${fromNumber}`,
                BusinessExceptionStatus.INVALID_INPUT,
              );
            }

            await tx.$executeRaw(
              Prisma.sql`
                UPDATE "message_hub"."outgoing_message"
                SET "sent"    = true,
                    "sent_at" = NOW()
                WHERE "id" = ${message.id}::uuid
              `,
            );
            const finishedSendingDate = new Date();
            logger.info(
              `Message sent successfully from phone: ${customerPhone.phoneNumber} to: ${message.to
              }. Took: ${finishedSendingDate.getTime() - startSendingDate.getTime()
              }ms. Message: ${JSON.stringify(message)}`,
            );
          } catch (error) {
            const newTimeToGo = new Date(
              message.timeToGo.getTime() + Number(maxOutgoingDelay) * 1000 * 5,
            );

            await tx.$executeRaw(
              Prisma.sql`
                UPDATE "message_hub"."outgoing_message"
                SET "time_to_go" = ${newTimeToGo.toISOString()}::timestamp
                WHERE "id" = ${message.id}::uuid
              `,
            );

            const finishedSendingDate = new Date();
            logger.error(
              `Failed to send message from phone: ${customerPhone.phoneNumber} to: ${message.to
              }. Took: ${finishedSendingDate.getTime() - startSendingDate.getTime()
              }ms. Message: ${JSON.stringify(message)}.Error: ${error.message}`,
            );
          }
        }
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 60000,
        timeout: 60000,
      },
    );
  }

  async getPendingOutgoingMessage(
    customerId: string,
    channel: CommunicationChannel,
    to: string,
  ): Promise<OutgoingMessageEntity[]> {
    return this.prisma.client.$transaction(
      async tx => {
        const messages = (await tx.$queryRaw(Prisma.sql`
          SELECT "id",
                 "from",
                 "to",
                 "message",
                 "time_to_go"            AS "timeToGo",
                 "communication_channel" AS "channel",
                 "api_url"               AS "apiUrl",
                 "message_type"          AS "messageType",
                 "file_url"              AS "fileUrl"
          FROM "message_hub"."outgoing_message"
          WHERE "sent" = false
            AND "customer_id" = ${Prisma.sql`${customerId}::uuid`}
            AND "to" = ${to}
            AND "communication_channel" = ${channel}
          ORDER BY "time_to_go" ASC
            FOR
              UPDATE SKIP LOCKED
        `)) as OutgoingMessageEntity[];

        if (!messages || messages.length === 0) {
          // logger.info(
          //   `No pending messages to send for customer: ${customerId} and channel: ${channel}...`,
          // );
          return [];
        }

        const customerPhone = await tx.customerPhone.findFirst({
          where: { customerId: customerId, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(
            `Customer phone not found for customer: ${customerId} and channel: ${channel}`,
          );
        }

        await tx.$executeRaw(
          Prisma.sql`
            UPDATE message_hub.outgoing_message om
            SET sent       = true,
                sent_at    = NOW(),
                updated_at = NOW()
            WHERE om.id IN (${Prisma.join(
            messages.map(message => Prisma.sql`${message.id}::uuid`),
          )});
          `,
        );

        return messages;
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 60000,
        timeout: 60000,
      },
    );
  }

  async getTimestampFromDatabase(): Promise<{ now: Date; nowToString: string }> {
    return this.prisma.client.$transaction(async tx => {
      const [currentDatabaseTime] = (await tx.$queryRaw(Prisma.sql`
            SELECT now() as now
        `)) as [{ now: Date }];
      return { now: currentDatabaseTime.now, nowToString: currentDatabaseTime.now.toString() };
    });
  }

  async getTotalFirstMessagesSentByPortfolio(
    customerId: string,
    portfolioId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{ date: string; count: number; total: number }[]> {
    const results = await this.prisma.client.$queryRaw<{ date: string; count: number }[]>(
      Prisma.sql`
        SELECT TO_CHAR(DATE(om.sent_at), 'YYYY-MM-DD') as date,
               COUNT(*) as count
        FROM message_hub.outgoing_message om
        INNER JOIN business_base.portfolio_item pi ON om.to = pi.phone_number
        WHERE om.customer_id = ${customerId}::uuid
          AND pi.portfolio_id = ${portfolioId}::uuid
          AND om.sent = true
          AND om.is_first_message = true
          AND om.sent_at >= ${dateStart.toISOString()}::timestamp
          AND om.sent_at <= ${dateEnd.toISOString()}::timestamp
          AND pi.status = 'ACTIVE'
          AND om.status != 'DELETED'
        GROUP BY DATE(om.sent_at)
        ORDER BY DATE(om.sent_at) ASC
      `,
    );

    // Calculate total count
    const total = results.reduce((sum, row) => sum + Number(row.count), 0);

    // Return results with total included in each row for consistency
    return results.map(row => ({
      date: row.date,
      count: Number(row.count),
      total,
    }));
  }
}
